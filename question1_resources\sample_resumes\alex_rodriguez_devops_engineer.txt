ALEX RODRIGUEZ
DevOps Engineer & Cloud Architect
Email: <EMAIL> | Phone: (*************
LinkedIn: linkedin.com/in/alexrodriguez | GitHub: github.com/alexrodriguez

PROFESSIONAL SUMMARY
Experienced DevOps Engineer with 6+ years of expertise in cloud infrastructure, automation, 
and CI/CD pipelines. Proven track record of reducing deployment time by 80% and improving 
system reliability to 99.99% uptime. Expert in AWS, Kubernetes, and Infrastructure as Code.

TECHNICAL SKILLS
• Cloud Platforms: AWS, Azure, Google Cloud Platform
• Containerization: Docker, Kubernetes, OpenShift
• Infrastructure as Code: Terraform, CloudFormation, Ansible
• CI/CD: Jenkins, GitLab CI, GitHub Actions, Azure DevOps
• Monitoring: Prometheus, Grafana, ELK Stack, Datadog, New Relic
• Scripting: Python, Bash, PowerShell, Go
• Version Control: Git, GitLab, Bitbucket
• Databases: PostgreSQL, MySQL, MongoDB, Redis

PROFESSIONAL EXPERIENCE

Senior DevOps Engineer | CloudTech Solutions | Jan 2021 - Present
• Architected and implemented AWS infrastructure serving 1M+ users
• Reduced deployment time from 2 hours to 15 minutes using automated pipelines
• Implemented monitoring and alerting systems achieving 99.99% uptime
• Led migration of legacy applications to Kubernetes clusters
• Managed infrastructure costs reducing monthly spend by 35%
• Mentored team of 4 junior DevOps engineers

DevOps Engineer | ScaleUp Technologies | Mar 2019 - Dec 2020
• Built CI/CD pipelines for 20+ microservices using Jenkins and Docker
• Automated infrastructure provisioning using Terraform and Ansible
• Implemented security best practices and compliance monitoring
• Optimized database performance reducing query response time by 50%
• Collaborated with development teams on application deployment strategies

Cloud Engineer | InfraCorp Ltd. | Jul 2017 - Feb 2019
• Managed AWS infrastructure for multiple client environments
• Implemented backup and disaster recovery solutions
• Automated server provisioning and configuration management
• Provided 24/7 on-call support for production systems
• Created documentation and runbooks for operational procedures

System Administrator | TechServices Inc. | Aug 2016 - Jun 2017
• Maintained Linux and Windows server environments
• Implemented monitoring solutions for network and server health
• Managed user accounts and security permissions
• Performed regular system updates and patch management
• Supported development teams with environment setup

EDUCATION
Bachelor of Science in Computer Science
Technology University | Graduated: May 2016
Concentration: Network and Systems Administration

CERTIFICATIONS
• AWS Certified Solutions Architect - Professional (2022)
• AWS Certified DevOps Engineer - Professional (2021)
• Certified Kubernetes Administrator (CKA) - 2020
• HashiCorp Certified: Terraform Associate (2021)
• Docker Certified Associate (2019)

PROJECTS & ACHIEVEMENTS
• Multi-Cloud Migration: Led migration of 50+ applications to hybrid cloud
• Cost Optimization: Reduced infrastructure costs by $200K annually
• Security Implementation: Achieved SOC 2 Type II compliance
• Disaster Recovery: Designed DR solution with 15-minute RTO

TECHNICAL PROJECTS
• Kubernetes Cluster Setup: Automated cluster provisioning with Terraform
• Monitoring Dashboard: Built comprehensive observability platform
• CI/CD Pipeline: Created zero-downtime deployment pipeline
• Infrastructure Automation: Reduced manual tasks by 90%

SPEAKING & COMMUNITY
• Speaker at DevOps Conference 2022: "Kubernetes Security Best Practices"
• AWS Community Builder (2021-Present)
• Contributor to open-source DevOps tools
• Technical blog writer with 10K+ monthly readers
