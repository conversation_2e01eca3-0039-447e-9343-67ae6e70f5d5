{"document_type_detection": {"prompt_template": "Analyze the following document text and determine its type. Choose from: contract, invoice, purchase_order, report, or other.\n\nDocument text:\n{document_text}\n\nReturn only the document type in lowercase.", "examples": [{"input": "SERVICE AGREEMENT CONTRACT... Party 1... Party 2... Signatures...", "output": "contract"}, {"input": "INVOICE... Invoice Number: INV-001... Amount: $1,000... Due Date...", "output": "invoice"}, {"input": "PURCHASE ORDER... PO Number: PO-001... Vendor... Ship To...", "output": "purchase_order"}]}, "missing_fields_detection": {"prompt_template": "Analyze this {document_type} document and identify which required fields are missing or incomplete.\n\nRequired fields for {document_type}:\n{required_fields_list}\n\nDocument text:\n{document_text}\n\nReturn a JSON response with:\n{\n  \"missing_fields\": [\"field1\", \"field2\"],\n  \"present_fields\": [\"field3\", \"field4\"],\n  \"suggestions\": [\"Add signature section\", \"Include due date\"]\n}", "field_validation": {"contract": "Look for party information, signatures, dates, payment terms, and service descriptions", "invoice": "Look for invoice number, amounts, due dates, tax information, and billing addresses", "purchase_order": "Look for PO number, vendor info, shipping address, total amount, and approval signatures", "report": "Look for title, date, author, summary, analysis, conclusions, and recommendations"}}, "document_improvement_suggestions": {"prompt_template": "Based on the missing fields in this {document_type}, provide specific suggestions for improvement.\n\nMissing fields: {missing_fields}\nDocument type: {document_type}\n\nProvide 3-5 actionable suggestions to make this document complete and legally/professionally sound.", "suggestion_categories": {"legal_compliance": "Suggestions for legal requirements and compliance", "professional_standards": "Suggestions for professional document standards", "clarity_improvements": "Suggestions for better clarity and understanding", "completeness": "Suggestions for missing information that should be included"}}, "field_extraction": {"prompt_template": "Extract the following information from this document:\n\nDocument text:\n{document_text}\n\nExtract these fields if present:\n{fields_to_extract}\n\nReturn JSON format:\n{\n  \"extracted_fields\": {\n    \"field_name\": \"extracted_value\",\n    \"another_field\": \"another_value\"\n  },\n  \"confidence_scores\": {\n    \"field_name\": 0.95,\n    \"another_field\": 0.80\n  }\n}", "extraction_rules": {"dates": "Look for date patterns like MM/DD/YYYY, Month DD, YYYY, or DD-MM-YYYY", "amounts": "Look for currency symbols followed by numbers, or words like 'total', 'amount', 'due'", "names": "Look for proper nouns, especially after titles like 'CEO', 'Manager', or in signature blocks", "addresses": "Look for street addresses, city, state, zip code patterns", "phone_numbers": "Look for phone number patterns like (XXX) XXX-XXXX or XXX-XXX-XXXX", "email_addresses": "Look for email patterns with @ symbol"}}, "document_quality_assessment": {"prompt_template": "Assess the overall quality and completeness of this {document_type} document.\n\nDocument text:\n{document_text}\n\nProvide assessment in JSON format:\n{\n  \"overall_score\": 85,\n  \"completeness_score\": 90,\n  \"clarity_score\": 80,\n  \"professional_score\": 85,\n  \"issues_found\": [\"Missing signature date\", \"Unclear payment terms\"],\n  \"strengths\": [\"Clear service description\", \"Complete contact information\"],\n  \"priority_fixes\": [\"Add signature dates\", \"Clarify payment schedule\"]\n}", "scoring_criteria": {"completeness": "All required fields present and filled out", "clarity": "Information is clear, unambiguous, and well-organized", "professional": "Document follows professional standards and formatting", "legal_soundness": "Document includes necessary legal protections and terms"}}}