# HACKATHON QUESTION PAPER 4
## Research Paper Analyzer

**Time:** 3 Hours | **Marks:** 100

### Problem Statement
Build a research assistant that summarizes papers and finds research gaps.

### Resources Provided
- **Research Papers**: Download 2-3 PDFs from https://arxiv.org/list/cs.AI/recent
- **OpenAI API Key**: Will be provided during event
- **Backup API**: https://jsonplaceholder.typicode.com/posts (use as mock papers)

### Core Requirements (Choose 3 out of 4)

**1. Paper Upload & Processing**
- Upload PDF research papers
- Extract text content
- Display paper information

**2. AI Summarization**
- Use AI to create paper summary
- Show key findings
- Extract main contributions

**3. Research Gap Detection**
- Use AI to identify what's missing in the research
- Suggest future work areas
- Show improvement suggestions

**4. Simple Search**
- Search through processed papers
- Find papers by topic
- Basic filtering

### Technical Requirements

**Must Have:**
- PDF text extraction
- AI summarization working
- Gap detection results
- Simple paper database

**Database Tables (Simple):**
```
papers: id, title, content, summary
gaps: paper_id, identified_gaps, suggestions
```

**API Endpoints (Minimum):**
```
POST /api/upload-paper - Upload paper
GET /api/summary/:id - Get paper summary
GET /api/gaps/:id - Get research gaps
```

### Demo Video (5 min max)
1. Upload paper and extract text (2 min)
2. Show AI-generated summary (2 min)
3. Display identified research gaps (1 min)

### Quick Start Tips
- Use basic PDF libraries for text extraction
- Simple AI prompts: "Summarize this paper" and "What research gaps exist?"
- Focus on text processing, skip figure analysis
- Process 2-3 papers maximum

**Goal: Working research analyzer in 3 hours!**