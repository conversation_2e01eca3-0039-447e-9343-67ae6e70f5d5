# Document Checker - Complete Setup Guide

## 📁 Resources Provided

### Sample Documents (Text Format for Easy PDF Conversion)
- `sample_documents/service_contract_complete.txt` - Complete contract with all required fields
- `sample_documents/service_contract_missing_fields.txt` - Contract missing key fields (signatures, dates)
- `sample_documents/invoice_complete.txt` - Complete invoice with all required fields
- `sample_documents/invoice_missing_fields.txt` - Invoice missing tax info and due date
- `sample_documents/purchase_order_complete.txt` - Complete purchase order with all fields

### Document Templates & Configuration
- `document_templates/required_fields.json` - Required fields for each document type
- `document_templates/document_analysis_prompts.json` - AI prompts for document analysis

### API Configuration
- OpenAI API Key: Will be provided during hackathon
- Document types supported: contract, invoice, purchase_order, report

## 🚀 Quick Start Instructions

### Step 1: Convert Text Documents to PDFs
```bash
# Option 1: Use online converters
# - Upload each .txt file to any text-to-PDF converter
# - Save as PDF in the same directory

# Option 2: Use Python script (provided below)
pip install reportlab
python convert_documents_to_pdf.py
```

### Step 2: Set Up Development Environment

#### Backend Setup (Node.js/Express)
```bash
mkdir document-checker-backend
cd document-checker-backend
npm init -y
npm install express multer pdf-parse openai cors dotenv sqlite3 sequelize
```

#### Frontend Setup (React)
```bash
npx create-react-app document-checker-frontend
cd document-checker-frontend
npm install axios react-router-dom
```

### Step 3: Environment Configuration
Create `.env` file in backend:
```env
OPENAI_API_KEY=your_openai_api_key_here
PORT=3001
DB_PATH=./documents.sqlite
```

### Step 4: Database Schema
```sql
-- documents table
CREATE TABLE documents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filename TEXT NOT NULL,
    original_name TEXT NOT NULL,
    document_type TEXT,
    content TEXT,
    file_size INTEGER,
    uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- analysis table
CREATE TABLE analysis (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER,
    document_type TEXT,
    missing_fields TEXT,
    present_fields TEXT,
    suggestions TEXT,
    completeness_score INTEGER,
    analysis_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents (id)
);

-- field_extractions table
CREATE TABLE field_extractions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER,
    field_name TEXT,
    field_value TEXT,
    confidence_score REAL,
    extracted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents (id)
);
```

## 🔧 Implementation Guide

### Core Features Implementation

#### 1. Document Upload & Text Extraction
```javascript
const multer = require('multer');
const pdfParse = require('pdf-parse');
const fs = require('fs');

const upload = multer({ dest: 'uploads/' });

app.post('/api/upload', upload.single('document'), async (req, res) => {
    try {
        const pdfBuffer = fs.readFileSync(req.file.path);
        const pdfData = await pdfParse(pdfBuffer);
        
        // Save document to database
        const document = await Document.create({
            filename: req.file.filename,
            original_name: req.file.originalname,
            content: pdfData.text,
            file_size: req.file.size
        });
        
        res.json({
            success: true,
            documentId: document.id,
            extractedText: pdfData.text,
            filename: req.file.originalname
        });
        
        // Clean up uploaded file
        fs.unlinkSync(req.file.path);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
```

#### 2. Document Type Detection
```javascript
const OpenAI = require('openai');
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

async function detectDocumentType(documentText) {
    const prompt = `Analyze the following document text and determine its type. 
    Choose from: contract, invoice, purchase_order, report, or other.
    
    Document text:
    ${documentText.substring(0, 2000)}...
    
    Return only the document type in lowercase.`;
    
    const response = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [{ role: "user", content: prompt }],
        max_tokens: 50
    });
    
    return response.choices[0].message.content.trim().toLowerCase();
}

app.post('/api/detect-type/:id', async (req, res) => {
    try {
        const document = await Document.findByPk(req.params.id);
        if (!document) {
            return res.status(404).json({ error: 'Document not found' });
        }
        
        const documentType = await detectDocumentType(document.content);
        
        // Update document with detected type
        await document.update({ document_type: documentType });
        
        res.json({ documentType });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
```

#### 3. Missing Field Detection
```javascript
const requiredFields = require('./document_templates/required_fields.json');

async function analyzeMissingFields(documentText, documentType) {
    const fields = requiredFields[documentType];
    if (!fields) {
        throw new Error(`Unknown document type: ${documentType}`);
    }
    
    const prompt = `Analyze this ${documentType} document and identify which required fields are missing or incomplete.

Required fields for ${documentType}:
${fields.required_fields.map(field => `- ${field}: ${fields.field_descriptions[field]}`).join('\n')}

Document text:
${documentText}

Return a JSON response with:
{
  "missing_fields": ["field1", "field2"],
  "present_fields": ["field3", "field4"],
  "suggestions": ["Add signature section", "Include due date"],
  "completeness_score": 75
}`;
    
    const response = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [{ role: "user", content: prompt }],
        max_tokens: 500
    });
    
    return JSON.parse(response.choices[0].message.content);
}

app.get('/api/analyze/:id', async (req, res) => {
    try {
        const document = await Document.findByPk(req.params.id);
        if (!document) {
            return res.status(404).json({ error: 'Document not found' });
        }
        
        if (!document.document_type) {
            return res.status(400).json({ error: 'Document type not detected yet' });
        }
        
        const analysis = await analyzeMissingFields(document.content, document.document_type);
        
        // Save analysis to database
        await Analysis.create({
            document_id: document.id,
            document_type: document.document_type,
            missing_fields: JSON.stringify(analysis.missing_fields),
            present_fields: JSON.stringify(analysis.present_fields),
            suggestions: JSON.stringify(analysis.suggestions),
            completeness_score: analysis.completeness_score
        });
        
        res.json(analysis);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
```

#### 4. Document Improvement Suggestions
```javascript
async function generateImprovementSuggestions(missingFields, documentType) {
    const prompt = `Based on the missing fields in this ${documentType}, provide specific suggestions for improvement.

Missing fields: ${missingFields.join(', ')}
Document type: ${documentType}

Provide 3-5 actionable suggestions to make this document complete and legally/professionally sound.
Return as JSON array: ["suggestion1", "suggestion2", "suggestion3"]`;
    
    const response = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [{ role: "user", content: prompt }],
        max_tokens: 300
    });
    
    return JSON.parse(response.choices[0].message.content);
}

app.get('/api/suggestions/:id', async (req, res) => {
    try {
        const analysis = await Analysis.findOne({
            where: { document_id: req.params.id },
            order: [['analysis_date', 'DESC']]
        });
        
        if (!analysis) {
            return res.status(404).json({ error: 'Analysis not found' });
        }
        
        const missingFields = JSON.parse(analysis.missing_fields);
        const suggestions = await generateImprovementSuggestions(missingFields, analysis.document_type);
        
        res.json({ suggestions });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
```

## 📝 PDF Conversion Script

Create `convert_documents_to_pdf.py`:
```python
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.utils import simpleSplit
import os

def text_to_pdf(text_file, pdf_file):
    c = canvas.Canvas(pdf_file, pagesize=letter)
    width, height = letter
    
    with open(text_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split content into lines that fit the page width
    lines = []
    for paragraph in content.split('\n'):
        if paragraph.strip():
            wrapped_lines = simpleSplit(paragraph, 'Helvetica', 10, width - 100)
            lines.extend(wrapped_lines)
        else:
            lines.append('')  # Empty line for spacing
    
    y = height - 50
    for line in lines:
        if y < 50:  # Start new page
            c.showPage()
            y = height - 50
        c.drawString(50, y, line)
        y -= 12
    
    c.save()

# Convert all document text files to PDFs
documents_dir = 'sample_documents'
for filename in os.listdir(documents_dir):
    if filename.endswith('.txt'):
        text_file = os.path.join(documents_dir, filename)
        pdf_file = os.path.join(documents_dir, filename.replace('.txt', '.pdf'))
        text_to_pdf(text_file, pdf_file)
        print(f"Converted {filename} to PDF")
```

## 🎯 Demo Script (5 minutes)

### Minute 1: Document Upload
1. Open the application
2. Upload a complete contract PDF
3. Show extracted text displayed on screen
4. Demonstrate file upload success

### Minutes 2-3: Document Type Detection & Analysis
1. Click "Analyze Document" button
2. Show AI detecting document type (contract/invoice)
3. Display analysis results with missing/present fields
4. Show completeness score

### Minutes 4-5: Missing Fields & Suggestions
1. Upload an incomplete document (missing fields version)
2. Show missing fields detection
3. Display specific improvement suggestions
4. Compare complete vs incomplete document analysis

## 🔍 Testing Scenarios

### Complete Documents (Should show high completeness scores)
- **service_contract_complete.pdf** → Should detect as "contract" with 90%+ completeness
- **invoice_complete.pdf** → Should detect as "invoice" with all required fields present

### Incomplete Documents (Should identify missing fields)
- **service_contract_missing_fields.pdf** → Should identify missing signatures and dates
- **invoice_missing_fields.pdf** → Should identify missing tax info and due date

## 🚨 Common Issues & Solutions

1. **PDF text extraction fails**: Ensure PDFs contain selectable text, not scanned images
2. **AI analysis errors**: Check OpenAI API key and request format
3. **Database connection issues**: Verify SQLite file permissions
4. **File upload problems**: Check multer configuration and upload directory permissions

## 📊 Success Metrics

Your working application should demonstrate:
- ✅ PDF document upload and text extraction
- ✅ AI-powered document type detection (contract/invoice/purchase_order)
- ✅ Missing field identification with specific field names
- ✅ Actionable improvement suggestions
- ✅ Completeness scoring and analysis results display

**Goal: Focus on accurate field detection and clear suggestions. A working analyzer that identifies 3-4 missing fields correctly is better than a complex system that doesn't work!**
