JOHN DOE
Software Engineer
Email: <EMAIL> | Phone: (*************

TECHNICAL SKILLS
• Programming: JavaScript, Python, Java, React, Node.js
• Databases: PostgreSQL, MongoDB, MySQL
• Cloud: AWS, Docker, Kubernetes
• Tools: Git, Jenkins, VS Code

EXPERIENCE
Senior Software Engineer | TechCorp Inc. | 2021-Present
• Developed web applications serving 100K+ users
• Built REST APIs with 99.9% uptime
• Mentored junior developers

Software Engineer | StartupXYZ | 2019-2020
• Created e-commerce platform using React and Django
• Integrated payment systems and third-party APIs
• Reduced bugs by 60% through automated testing

EDUCATION
Bachelor of Computer Science | State University | 2018
GPA: 3.7/4.0

PROJECTS
• Personal Finance Tracker: React/Node.js app
• Weather API Service: Python Flask microservice
• Task Management System: Vue.js/Django app
