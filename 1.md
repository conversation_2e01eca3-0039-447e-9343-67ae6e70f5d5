# HACKATHON QUESTION 1
## LLM-Powered Job Portal

**Time Limit: 3 Hours**

### Problem Statement
Build a job portal that uses Large Language Models (LLM) to match candidates with jobs based on skills analysis.

### What You'll Build
A web application where users can upload resumes, view job listings, and get LLM-powered job recommendations with match scores.

### Resources Provided
- **Resume Templates**: question1_resources/sample_resumes/ (text files - convert to PDF)
- **Job Data Source**: https://jsonplaceholder.typicode.com/posts (adapt as job listings)
- **LLM Access**: Use any LLM API (OpenAI, Anthropic, Google, Hugging Face, or local models)

### Core Features (Implement 3 out of 4)

**1. Job Listings & Search**
- Display jobs from API in a clean list
- Add search functionality by job title
- Include basic filters (location, company)

**2. Resume Processing**
- Upload PDF resume functionality
- Extract and display text content from PDF
- Store resume data in database

**3. LLM Skills Analysis**
- Use LLM to extract skills from job descriptions
- Extract skills from uploaded resume text
- Calculate and display match percentage

**4. Smart Recommendations**
- Show job recommendations with match scores
- Display matching skills vs missing skills
- Provide explanations for match results

### Technical Requirements
- **Frontend**: 2-3 pages (job list, upload, results)
- **Backend**: 3 API endpoints minimum
- **Database**: SQLite with 3 tables (jobs, candidates, matches)
- **LLM Integration**: Any LLM API for skill extraction and matching

### Success Criteria
- Users can upload resumes and see extracted text
- Job listings display with search functionality
- LLM generates meaningful match scores with explanations
- Clean, functional interface (UI polish not required)

### Time Management Tips
- **Hour 1**: Set up project, job listings, basic UI
- **Hour 2**: Resume upload, PDF text extraction
- **Hour 3**: LLM integration, matching algorithm, testing

**Focus on functionality over design - a working prototype beats a beautiful non-functional app!**