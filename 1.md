# HACKATHON QUESTION PAPER 1
## Job Portal with Smart Matching

**Time:** 3 Hours | **Marks:** 100

### Problem Statement
Build a simple job portal that matches candidates with jobs using AI.

### Resources Provided
- **Sample Resumes**: 3-5 PDF files you create
- **OpenAI API Key**: Will be provided during event
- **Job API**: https://jsonplaceholder.typicode.com/posts (use as job listings)

### Core Requirements (Choose 3 out of 4)

**1. Job Display Page**
- Fetch jobs from API and show in a list
- Basic search by job title
- Simple filters (location, company)

**2. Resume Upload**
- Upload PDF resume
- Extract text from PDF (use any PDF library)
- Show extracted text on screen

**3. AI Skill Matching** 
- Use AI to find skills in job descriptions
- Extract skills from resume text
- Calculate simple match percentage

**4. Match Results**
- Show jobs with match percentages
- Simple explanation (matching skills vs missing skills)

### Technical Requirements

**Must Have:**
- Working frontend with 2-3 pages
- Backend with 2-3 API endpoints  
- Basic database (3 tables max)
- AI integration working

**Database Tables (Simple):**
```
jobs: id, title, description
candidates: id, name, skills_text  
matches: job_id, candidate_id, score
```

**API Endpoints (Minimum):**
```
GET /api/jobs - List jobs
POST /api/upload - Upload resume
POST /api/match - Calculate match
```

### Demo Video (5 min max)
1. Show job listings (1 min)
2. Upload resume and extract text (2 min)  
3. Show match results (2 min)

### Quick Start Tips
- Start with job listing first
- Use simple PDF text extraction
- Keep AI prompts basic: "Find skills in this text: [text]"
- Focus on core functionality, skip fancy UI

**Goal: Working app in 3 hours, not perfect app!**