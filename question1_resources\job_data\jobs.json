[{"id": 1, "title": "Senior Full Stack Developer", "company": "TechCorp Inc.", "location": "San Francisco, CA", "type": "Full-time", "salary": "$120,000 - $150,000", "description": "We are seeking a Senior Full Stack Developer to join our growing team. You will be responsible for developing and maintaining web applications using modern technologies including React, Node.js, and PostgreSQL. The ideal candidate has 5+ years of experience in full-stack development and is passionate about creating scalable, high-performance applications.", "requirements": ["5+ years of experience in full-stack development", "Proficiency in JavaScript, React, Node.js", "Experience with PostgreSQL or similar databases", "Knowledge of RESTful APIs and GraphQL", "Experience with cloud platforms (AWS, Azure, or GCP)", "Strong problem-solving and communication skills"], "skills": ["JavaScript", "React", "Node.js", "PostgreSQL", "AWS", "REST APIs", "GraphQL", "HTML", "CSS"], "posted_date": "2024-01-15", "application_deadline": "2024-02-15"}, {"id": 2, "title": "Data Scientist - Machine Learning", "company": "DataTech Solutions", "location": "New York, NY", "type": "Full-time", "salary": "$110,000 - $140,000", "description": "Join our data science team to build cutting-edge machine learning models that drive business decisions. You will work with large datasets, develop predictive models, and collaborate with cross-functional teams to deliver actionable insights. Experience with Python, TensorFlow, and statistical analysis is required.", "requirements": ["Master's degree in Data Science, Statistics, or related field", "3+ years of experience in machine learning and data analysis", "Proficiency in Python, R, and SQL", "Experience with TensorFlow, PyTorch, or similar ML frameworks", "Strong statistical analysis and data visualization skills", "Experience with cloud platforms and big data tools"], "skills": ["Python", "Machine Learning", "TensorFlow", "SQL", "Statistics", "Data Visualization", "R", "AWS", "<PERSON><PERSON>", "NumPy"], "posted_date": "2024-01-12", "application_deadline": "2024-02-12"}, {"id": 3, "title": "Senior Product Manager", "company": "InnovateApp Inc.", "location": "Austin, TX", "type": "Full-time", "salary": "$130,000 - $160,000", "description": "We're looking for a Senior Product Manager to lead our mobile app product strategy. You will define product roadmaps, work with engineering and design teams, and drive user engagement initiatives. The ideal candidate has experience in mobile product management and a track record of successful product launches.", "requirements": ["5+ years of product management experience", "Experience with mobile app development and strategy", "Strong analytical and data-driven decision making skills", "Experience with Agile/Scrum methodologies", "Excellent communication and leadership skills", "MBA or equivalent experience preferred"], "skills": ["Product Management", "Mobile Apps", "Agile", "Scrum", "Analytics", "User Research", "Roadmap Planning", "Stakeholder Management"], "posted_date": "2024-01-10", "application_deadline": "2024-02-10"}, {"id": 4, "title": "UX/UI Designer", "company": "DesignForward Agency", "location": "Los Angeles, CA", "type": "Full-time", "salary": "$85,000 - $110,000", "description": "Join our creative team as a UX/UI Designer to create beautiful and intuitive user experiences. You will conduct user research, create wireframes and prototypes, and collaborate with developers to bring designs to life. Experience with Figma, user research, and responsive design is essential.", "requirements": ["3+ years of UX/UI design experience", "Proficiency in Figma, Sketch, or similar design tools", "Experience with user research and usability testing", "Strong portfolio demonstrating design process and outcomes", "Knowledge of responsive design and accessibility principles", "Bachelor's degree in Design or related field"], "skills": ["UX Design", "UI Design", "Figma", "User Research", "Prototyping", "Responsive Design", "Accessibility", "Usability Testing"], "posted_date": "2024-01-08", "application_deadline": "2024-02-08"}, {"id": 5, "title": "DevOps Engineer", "company": "CloudTech Solutions", "location": "Seattle, WA", "type": "Full-time", "salary": "$115,000 - $145,000", "description": "We are seeking a DevOps Engineer to manage our cloud infrastructure and CI/CD pipelines. You will work with AWS, Kubernetes, and automation tools to ensure reliable and scalable deployments. The ideal candidate has experience with containerization, infrastructure as code, and monitoring systems.", "requirements": ["4+ years of DevOps or cloud engineering experience", "Strong experience with AWS, Azure, or Google Cloud", "Proficiency in Kubernetes and Docker", "Experience with Infrastructure as Code (Terraform, CloudFormation)", "Knowledge of CI/CD pipelines and automation tools", "Scripting skills in Python, Bash, or similar languages"], "skills": ["DevOps", "AWS", "Kubernetes", "<PERSON>er", "Terraform", "CI/CD", "Python", "Monitoring", "Infrastructure as Code"], "posted_date": "2024-01-05", "application_deadline": "2024-02-05"}, {"id": 6, "title": "Frontend Developer - React", "company": "WebSolutions LLC", "location": "Remote", "type": "Full-time", "salary": "$90,000 - $115,000", "description": "Join our remote team as a Frontend Developer specializing in React applications. You will build responsive user interfaces, optimize performance, and collaborate with backend developers to integrate APIs. Strong JavaScript and React skills are required.", "requirements": ["3+ years of frontend development experience", "Expert-level knowledge of React and JavaScript", "Experience with modern frontend tools and workflows", "Knowledge of responsive design and cross-browser compatibility", "Experience with state management (Redux, Context API)", "Strong attention to detail and code quality"], "skills": ["React", "JavaScript", "HTML", "CSS", "Redux", "Responsive Design", "Git", "Webpack", "TypeScript"], "posted_date": "2024-01-03", "application_deadline": "2024-02-03"}, {"id": 7, "title": "Backend Developer - Python", "company": "StartupXYZ", "location": "Boston, MA", "type": "Full-time", "salary": "$95,000 - $125,000", "description": "We're looking for a Backend Developer with strong Python skills to build scalable APIs and services. You will work with Django/Flask, databases, and cloud services to support our growing platform. Experience with microservices architecture is a plus.", "requirements": ["4+ years of backend development experience", "Strong proficiency in Python and web frameworks (Django/Flask)", "Experience with relational and NoSQL databases", "Knowledge of RESTful API design and development", "Experience with cloud services and containerization", "Understanding of software architecture and design patterns"], "skills": ["Python", "Django", "Flask", "PostgreSQL", "MongoDB", "REST APIs", "<PERSON>er", "AWS", "Microservices"], "posted_date": "2024-01-01", "application_deadline": "2024-02-01"}, {"id": 8, "title": "Mobile App Developer - iOS", "company": "MobileFirst Corp", "location": "Chicago, IL", "type": "Full-time", "salary": "$100,000 - $130,000", "description": "Join our mobile development team to create innovative iOS applications. You will work with Swift, UIKit, and modern iOS frameworks to deliver high-quality mobile experiences. Experience with App Store deployment and mobile app architecture is required.", "requirements": ["3+ years of iOS development experience", "Proficiency in Swift and Objective-C", "Experience with UIKit, SwiftUI, and iOS frameworks", "Knowledge of mobile app architecture patterns (MVVM, MVC)", "Experience with App Store submission process", "Understanding of mobile UI/UX best practices"], "skills": ["iOS", "Swift", "UIKit", "SwiftUI", "Xcode", "Mobile Development", "App Store", "Core Data"], "posted_date": "2023-12-28", "application_deadline": "2024-01-28"}]