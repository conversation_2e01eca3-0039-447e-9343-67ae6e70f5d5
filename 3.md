# HACKATHON QUESTION 3
## Smart Price Tracker

**Time Limit: 3 Hours**

### Problem Statement
Build an intelligent price monitoring system that tracks product prices over time and analyzes customer reviews using AI sentiment analysis.

### What You'll Build
A web application that displays products, simulates price changes, tracks price history, and provides AI-powered review sentiment analysis.

### Resources Provided
- **Product Data API**: https://fakestoreapi.com/products (20 products with reviews)
- **OpenAI API Key**: Provided during hackathon
- **Price Simulation**: You'll implement random price fluctuations

### Core Features (Implement 3 out of 4)

**1. Product Catalog & Search**
- Fetch and display products from the API
- Show current prices, ratings, and basic info
- Implement search and category filtering

**2. Price History Tracking**
- Simulate price changes over time (±5-15% random variations)
- Store historical price data in database
- Display price trends with simple charts

**3. AI Review Sentiment Analysis**
- Use AI to analyze customer review sentiment
- Classify reviews as positive, negative, or neutral
- Generate sentiment scores and summaries

**4. Price Comparison Dashboard**
- Create visual price trend charts
- Show price alerts for significant changes
- Compare products by price history

### Technical Requirements
- **Frontend**: Product listing, price charts, review analysis display
- **Backend**: API integration, price simulation, AI processing
- **Database**: SQLite for products, price history, and review analysis
- **Visualization**: Charts for price trends (Chart.js or similar)

### Success Criteria
- Products load from API with current prices and reviews
- Price history simulation works with visual trends
- AI accurately analyzes review sentiment
- Clean dashboard showing price insights

### Time Management Tips
- **Hour 1**: API integration, product display, database setup
- **Hour 2**: Price simulation, history tracking, basic charts
- **Hour 3**: AI review analysis, dashboard polish, testing

**Focus on data accuracy and meaningful insights - users should trust your price recommendations!**