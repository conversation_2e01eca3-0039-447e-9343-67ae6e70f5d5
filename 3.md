# HACKATHON QUESTION PAPER 3
## Price Tracker

**Time:** 3 Hours | **Marks:** 100

### Problem Statement
Build a simple price tracker that monitors product prices and analyzes reviews.

### Resources Provided
- **Product API**: https://fakestoreapi.com/products (20 products with reviews)
- **OpenAI API Key**: Will be provided during event
- **Sample Catalog**: Create 1-2 PDF files with product lists

### Core Requirements (Choose 3 out of 4)

**1. Product Display**
- Fetch products from API
- Show products with prices
- Basic search functionality

**2. Price History**
- Store price data over time (simulate price changes)
- Show simple price trend (up/down/same)

**3. Review Analysis**
- Use AI to analyze product reviews
- Show sentiment (positive/negative)
- Simple rating summary

**4. PDF Catalog Processing**
- Extract product info from PDF catalog
- Compare with API data
- Show price differences

### Technical Requirements

**Must Have:**
- Product listing page
- Basic price tracking
- AI review analysis
- Simple data visualization

**Database Tables (Simple):**
```
products: id, name, price, rating
price_history: product_id, old_price, new_price, date
reviews: product_id, sentiment, score
```

**API Endpoints (Minimum):**
```
GET /api/products - List products
POST /api/track - Track product price
GET /api/reviews/:id - Get review analysis
```

### Demo Video (5 min max)
1. Show product listing (1 min)
2. Display price changes (2 min)
3. Review sentiment analysis (2 min)

### Quick Start Tips
- Use chart library for simple price graphs
- Basic AI prompt: "Is this review positive or negative?"
- Simulate price changes by adding random amounts
- Focus on core tracking features

**Goal: Working price tracker in 3 hours!**