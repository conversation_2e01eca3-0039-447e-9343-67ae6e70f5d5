{"contract": {"required_fields": ["party_1", "party_2", "signature", "date", "contract_terms", "payment_terms", "services_description"], "field_descriptions": {"party_1": "First party/service provider information including name and contact details", "party_2": "Second party/client information including name and contact details", "signature": "Signatures from both parties or authorized representatives", "date": "Contract execution date or effective date", "contract_terms": "Duration, start date, end date, or term specifications", "payment_terms": "Payment schedule, amounts, due dates, and payment methods", "services_description": "Detailed description of services or deliverables"}, "validation_patterns": {"date": "\\d{1,2}[/-]\\d{1,2}[/-]\\d{4}|\\w+ \\d{1,2}, \\d{4}", "signature": "signature|signed|\\[.*signature.*\\]", "payment_terms": "payment|\\$|amount|due|fee", "party_1": "party 1|service provider|contractor|vendor", "party_2": "party 2|client|customer|buyer"}}, "invoice": {"required_fields": ["invoice_number", "amount", "due_date", "tax", "bill_to", "bill_from", "invoice_date"], "field_descriptions": {"invoice_number": "Unique invoice identifier or reference number", "amount": "Total amount due or subtotal with clear monetary values", "due_date": "Payment due date or payment terms", "tax": "Tax amount, tax rate, or tax identification information", "bill_to": "Customer/client billing information and address", "bill_from": "Vendor/service provider information and address", "invoice_date": "Date the invoice was issued or generated"}, "validation_patterns": {"invoice_number": "invoice.{0,10}(number|#|no\\.?):?\\s*[A-Z0-9-]+", "amount": "\\$[0-9,]+\\.?\\d*|total.{0,20}\\$|amount.{0,20}\\$", "due_date": "due.{0,20}date|payment.{0,20}due|due.{0,20}\\d{1,2}[/-]\\d{1,2}", "tax": "tax|\\d+\\.?\\d*%|tax.{0,10}\\$", "bill_to": "bill to|customer|client", "bill_from": "bill from|from:|vendor|company"}}, "purchase_order": {"required_fields": ["po_number", "vendor_info", "ship_to", "total_amount", "approval", "delivery_date", "items_ordered"], "field_descriptions": {"po_number": "Purchase order number or reference identifier", "vendor_info": "Vendor/supplier name and contact information", "ship_to": "Shipping address and delivery location", "total_amount": "Total purchase amount including taxes and fees", "approval": "Approval signatures or authorization information", "delivery_date": "Required delivery date or shipping timeline", "items_ordered": "Detailed list of items, quantities, and prices"}, "validation_patterns": {"po_number": "po.{0,10}(number|#|no\\.?):?\\s*[A-Z0-9-]+", "vendor_info": "vendor|supplier|from:", "ship_to": "ship to|deliver to|shipping address", "total_amount": "total.{0,20}\\$|amount.{0,20}\\$", "approval": "approval|approved|authorized|signature", "delivery_date": "delivery.{0,20}date|required.{0,20}date|ship.{0,20}date", "items_ordered": "item|product|quantity|qty|description"}}, "report": {"required_fields": ["title", "date", "author", "summary", "data_analysis", "conclusions", "recommendations"], "field_descriptions": {"title": "Report title or subject matter", "date": "Report creation date or reporting period", "author": "Report author or responsible party", "summary": "Executive summary or overview section", "data_analysis": "Data analysis, findings, or methodology section", "conclusions": "Conclusions or key findings section", "recommendations": "Recommendations or next steps section"}, "validation_patterns": {"title": "title|subject|report on", "date": "date|\\d{1,2}[/-]\\d{1,2}[/-]\\d{4}", "author": "author|prepared by|written by", "summary": "summary|overview|executive summary", "data_analysis": "analysis|findings|data|methodology|results", "conclusions": "conclusion|findings|results", "recommendations": "recommendation|next steps|action items"}}}