JOHN DOE
Software Engineer
Email: <EMAIL> | Phone: (*************
LinkedIn: linkedin.com/in/johndoe | GitHub: github.com/johndoe

PROFESSIONAL SUMMARY
Experienced Full-Stack Software Engineer with 5+ years developing scalable web applications. 
Proficient in JavaScript, Python, React, Node.js, and cloud technologies. Strong problem-solving 
skills and experience with agile development methodologies.

TECHNICAL SKILLS
• Programming Languages: JavaScript, Python, Java, TypeScript, SQL
• Frontend: React, Vue.js, HTML5, CSS3, Bootstrap, Tailwind CSS
• Backend: Node.js, Express.js, Django, Flask, REST APIs, GraphQL
• Databases: PostgreSQL, MongoDB, MySQL, Redis
• Cloud & DevOps: AWS, Docker, Kubernetes, CI/CD, Jenkins
• Tools: Git, Jira, Postman, VS Code, IntelliJ IDEA

PROFESSIONAL EXPERIENCE

Senior Software Engineer | TechCorp Inc. | Jan 2021 - Present
• Developed and maintained 3 high-traffic web applications serving 100K+ users
• Built RESTful APIs using Node.js and Express.js with 99.9% uptime
• Implemented React frontend components with responsive design
• Optimized database queries reducing response time by 40%
• Mentored 2 junior developers and conducted code reviews

Software Engineer | StartupXYZ | Jun 2019 - Dec 2020
• Created full-stack e-commerce platform using React and Django
• Integrated payment systems (Stripe, PayPal) and third-party APIs
• Implemented automated testing reducing bugs by 60%
• Collaborated with cross-functional teams using Agile methodology

Junior Developer | WebSolutions LLC | Aug 2018 - May 2019
• Developed responsive websites using HTML, CSS, and JavaScript
• Maintained legacy PHP applications and MySQL databases
• Participated in daily standups and sprint planning meetings

EDUCATION
Bachelor of Science in Computer Science
State University | Graduated: May 2018
GPA: 3.7/4.0

PROJECTS
• Personal Finance Tracker: React/Node.js app with data visualization
• Weather API Service: Python Flask microservice with Redis caching
• Task Management System: Vue.js frontend with Django REST backend

CERTIFICATIONS
• AWS Certified Developer Associate (2022)
• MongoDB Certified Developer (2021)
