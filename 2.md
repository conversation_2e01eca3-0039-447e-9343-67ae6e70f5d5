# H<PERSON><PERSON>THON QUESTION PAPER 2
## Document Checker

**Time:** 3 Hours | **Marks:** 100

### Problem Statement
Build a document analyzer that finds missing information in business documents.

### Resources Provided
- **Sample Document Templates**: Text templates provided (convert to PDF yourself)
- **OpenAI API Key**: Will be provided during event
- **Document Data**: question2_resources/sample_documents/ (text files - you convert to PDF)
- **Required Fields Template**:
```json
{"contract": ["party_1", "signature", "date"], "invoice": ["amount", "due_date", "tax"]}
```

### Core Requirements (Choose 3 out of 4)

**1. Document Upload**
- Upload PDF documents
- Extract text content
- Display extracted text

**2. Document Type Detection**
- Use AI to identify document type (contract/invoice/report)
- Show document type on screen

**3. Missing Field Detection**
- Check if required fields exist in document
- Use AI to find missing information
- Show list of missing fields

**4. Simple Report**
- Display what's missing
- Basic suggestions for fixes

### Technical Requirements

**Must Have:**
- File upload working
- PDF text extraction
- AI integration for analysis
- Simple results display

**Database Tables (Simple):**
```
documents: id, filename, type, content
analysis: document_id, missing_fields, suggestions
```

**API Endpoints (Minimum):**
```
POST /api/upload - Upload document
GET /api/analyze/:id - Get analysis results
```

### Demo Video (5 min max)
1. Upload document (1 min)
2. Show document type detection (2 min)
3. Display missing fields analysis (2 min)

### Quick Start Tips
- Use basic PDF libraries for text extraction
- Simple AI prompt: "Is this a contract or invoice? What fields are missing?"
- Focus on 2 document types only
- Skip image processing if running out of time

**Goal: Working document analyzer in 3 hours!**