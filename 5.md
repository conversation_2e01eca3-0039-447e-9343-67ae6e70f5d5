# HACKATHON QUESTION 5
## Natural Language to SQL Dashboard

**Time Limit: 3 Hours**

### Problem Statement
Build an intelligent business dashboard that converts natural language questions into SQL queries and visualizes business insights with LLM-powered analytics.

### What You'll Build
A web application where users can ask business questions in plain English, get LLM-generated SQL queries, and see results visualized in charts and KPIs.

### Resources Provided
- **Dataset Sources**: Choose and download from Kaggle
  - E-commerce Sales: https://www.kaggle.com/datasets/carrie1/ecommerce-data
  - Retail Dataset: https://www.kaggle.com/datasets/mohammadtalib786/retail-sales-dataset
  - Customer Data: https://www.kaggle.com/datasets/jackdaoud/marketing-data
- **LLM Access**: Use any LLM API (OpenAI, Anthropic, Google, Hugging Face, or local models)

### Data Setup Challenge
You must download, clean, and structure Kaggle data into these tables:
```sql
customers: id, name, email, city, signup_date
products: id, name, category, price, stock
orders: id, customer_id, product_id, quantity, order_date, total
sales: id, order_id, revenue, profit_margin, sales_date
```

### Core Features (Implement 3 out of 4)

**1. Natural Language Query Interface**
- Text input for business questions in plain English
- LLM converts questions to SQL queries
- Execute queries and display results in tables
- Example: "Show top 5 customers by revenue" → SQL → Results

**2. Business KPI Dashboard**
- Calculate and display key metrics (revenue, orders, customers)
- Real-time data from your database
- Visual KPI cards with trend indicators
- Auto-refresh dashboard data

**3. Interactive Data Visualization**
- Generate charts from query results (bar, line, pie)
- Sales trends, product performance, customer analytics
- Filter data by date ranges and categories
- Export chart data and insights

**4. Smart Query Management**
- Save successful queries for reuse
- Pre-built business question templates
- Query history and performance tracking
- LLM-suggested follow-up questions

### Technical Requirements
- **Frontend**: Query interface, dashboard, chart visualization
- **Backend**: LLM integration, SQL execution, data processing
- **Database**: SQLite with business data from Kaggle
- **LLM Integration**: Natural language to SQL conversion
- **Data Setup**: Download, clean, and import Kaggle dataset

### Success Criteria
- Users can ask business questions in natural language
- LLM generates accurate SQL queries from questions
- Dashboard displays meaningful KPIs with real data
- Charts visualize data insights effectively
- Query results are accurate and well-formatted

### Example Questions to Support
- "Show me total sales this year"
- "Which products sold the most?"
- "Who are our top 10 customers by revenue?"
- "What's the monthly sales trend?"
- "Which product category is most profitable?"

### Time Management Tips
- **Hour 1**: Download Kaggle data, clean and import to SQLite, basic project setup
- **Hour 2**: Natural language to SQL conversion, query execution, results display
- **Hour 3**: KPI dashboard, data visualization, testing and polish

### Critical Success Factors
1. **Data Quality**: Clean, well-structured database is essential
2. **LLM Prompting**: Craft precise prompts for accurate SQL generation
3. **Error Handling**: Handle invalid queries and edge cases gracefully
4. **User Experience**: Clear interface for asking questions and viewing results

**Focus on getting basic NL-to-SQL working before adding advanced features!**