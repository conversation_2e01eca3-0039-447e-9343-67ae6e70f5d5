# HACKATHON QUESTION PAPER 6
## Natural Language to SQL Dashboard

**Time:** 3 Hours | **Marks:** 100

### Problem Statement
Build a business intelligence dashboard that converts natural language questions to SQL queries and visualizes KPIs.

### Resources Provided
- **Sample Database**: SQLite file with business data (provided)
- **Database Schema**: Sales, customers, products, orders tables
- **OpenAI API Key**: Will be provided during event
- **Sample Data**: 1000+ records of e-commerce data

### Database Schema (Pre-loaded)
```sql
customers: id, name, email, city, signup_date
products: id, name, category, price, stock
orders: id, customer_id, product_id, quantity, order_date, total
sales: id, order_id, revenue, profit_margin, sales_date
```

### Core Requirements (Choose 3 out of 4)

**1. Natural Language Query Interface**
- Input box for natural language questions
- Convert text to SQL using AI
- Execute SQL and show results
- Examples: "Show top 5 customers by revenue", "Sales by month"

**2. KPI Dashboard**
- Display key business metrics (revenue, orders, customers)
- Real-time calculations from database
- Visual cards/widgets for KPIs
- Auto-refresh capabilities

**3. Data Visualization**
- Charts for sales trends, product performance
- Interactive graphs (bar, line, pie charts)
- Filter by date ranges, categories
- Export chart data

**4. Query History & Templates**
- Save successful queries for reuse
- Pre-built question templates
- Query performance tracking
- Popular queries suggestions

### Technical Requirements

**Must Have:**
- Natural language input processing
- SQL query generation and execution
- KPI calculation and display
- Basic data visualization

**API Endpoints (Minimum):**
```
POST /api/nl-to-sql - Convert natural language to SQL
GET /api/kpis - Get dashboard KPIs
POST /api/execute-query - Execute generated SQL
GET /api/chart-data/:type - Get data for charts
```

### Visual Examples

**Query Interface:**
```
Ask anything about your business data:
[What were the sales last month?_________] [Ask AI]

Generated SQL:
SELECT SUM(revenue) FROM sales 
WHERE sales_date >= '2024-08-01' 
AND sales_date < '2024-09-01';

Results: $45,230 in sales last month
```

**KPI Dashboard:**
```
Business Overview

📊 Total Revenue      🛒 Total Orders      👥 Active Customers
   $234,567             1,245                 892

📈 Monthly Growth: +12%  📦 Top Product: iPhone  🏙️ Top City: NYC
```

**Chart Examples:**
```
Sales Trend (Line Chart)
Revenue by Category (Pie Chart)  
Top Products (Bar Chart)
Customer Growth (Area Chart)
```

### Sample Natural Language Questions
- "Show me total sales this year"
- "Which products sold the most?"
- "Who are our top 10 customers?"
- "What's the revenue by city?"
- "Show monthly sales trend"
- "Which category is most profitable?"

### Demo Video (5 min max)
1. Ask natural language question and show SQL generation (2 min)
2. Display KPI dashboard with live data (2 min)
3. Show interactive charts and visualizations (1 min)

### AI Integration Examples
**Natural Language to SQL Prompt:**
```
"Convert this question to SQL for database with tables: customers, products, orders, sales.
Question: 'Show top 5 customers by revenue'
Return only the SQL query."
```

**KPI Analysis Prompt:**
```
"Analyze these business metrics: [data]. 
Provide insights about performance trends and recommendations."
```

### Quick Start Tips
- Start with simple SQL queries (SELECT, SUM, COUNT)
- Use chart libraries like Chart.js for visualizations
- Test AI prompts with basic questions first
- Pre-populate database with sample data
- Focus on 3-4 main KPIs initially

**Goal: Working natural language to SQL dashboard in 3 hours!**